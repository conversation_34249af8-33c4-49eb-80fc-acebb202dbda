<?php

namespace App\Console\Commands;

use Exception;
use App\Services\AkeylessService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Message Encryption Migration Command
 *
 * This command handles migration/re-encryption of message content from old key to new Akeyless key.
 * Features:
 * - Processes content_text and quote_text fields together per record
 * - Batch processing with progress tracking
 * - Transaction safety with rollback capability
 * - Dry-run mode for testing
 */
class MessageKeyMigrationCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'messages:encrypt-migration
                            {--dry-run : Preview migration without executing}
                            {--batch=1000 : Number of records per batch}
                            {--old-version= : Version of old key to retrieve from <PERSON>key<PERSON> (if not using config)}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate message encryption from old key to new Akeyless key';

    private $akeylessService;
    private $oldKey;
    private $newKey;

    /**
     * Fields to process during migration
     */
    private const MIGRATION_FIELDS = ['content_text', 'quote_text'];

    /**
     * Constants for logging and display
     */
    private const LOG_PREVIEW_LENGTH = 50;
    private const CHUNK_LOG_FREQUENCY = 10;

    public function __construct()
    {
        parent::__construct();
        $this->akeylessService = new AkeylessService();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $batchSize = (int) $this->option('batch');

        try {
            // Initialize and validate keys
            if (!$this->initializeEncryptionKeys()) {
                return 1;
            }

            // Show migration preview
            $stats = $this->displayMigrationPreview($isDryRun);

            // For dry-run, skip confirmation and proceed to execution
            if (!$isDryRun) {
                // Confirm migration execution for real run
                if (!$this->confirmMigrationExecution($stats)) {
                    $this->info('Migration cancelled by user');
                    return 0;
                }
            }

            // Execute migration (both dry-run and real)
            $migrationResult = $this->executeMigrationProcess($batchSize, $stats);
            return $migrationResult ? 0 : 1;
        } catch (Exception $e) {
            $this->error('Fatal error occurred: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Initialize and validate encryption keys
     */
    private function initializeEncryptionKeys(): bool
    {
        try {
            // Retrieve old key (from config or Akeyless with specific version)
            $oldVersion = $this->option('old-version');
            if ($oldVersion !== null) {
                $oldVersionInt = (int) $oldVersion;
                if ($oldVersionInt <= 0) {
                    $this->error("Invalid version: {$oldVersion}. Version must be a positive integer.");
                    return false;
                }

                $keyPath = config('services.akeyless.secret_path');
                $this->info("Retrieving old key from Akeyless version {$oldVersionInt}: {$keyPath}");
                $this->oldKey = $this->akeylessService->getSecret($keyPath, $oldVersionInt);
            } else {
                $this->info('Retrieving old key from application config');
                $this->oldKey = config('app.messages_encryption_key');
            }

            // Retrieve new key from Akeyless (latest version)
            $keyPath = config('services.akeyless.secret_path');
            $this->info("Retrieving new key from Akeyless (latest): {$keyPath}");
            $this->newKey = $this->akeylessService->getSecret($keyPath);

            // Validate keys exist
            if (empty($this->oldKey) || empty($this->newKey)) {
                $this->error('Failed to retrieve encryption keys');
                return false;
            }

            // Validate key compatibility
            if (strlen($this->oldKey) !== strlen($this->newKey)) {
                $this->error('Key length mismatch - cannot proceed');
                return false;
            }

            $this->info('Key validation successful - Length: ' . strlen($this->oldKey) . ' characters');
            return true;
        } catch (Exception $e) {
            $this->error('Key initialization failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Display migration preview and statistics
     */
    private function displayMigrationPreview(bool $isDryRun): array
    {
        $stats = $this->calculateMigrationStatistics();

        $title = $isDryRun ? '=== DRY RUN PREVIEW ===' : '=== MIGRATION PREVIEW ===';
        $this->info($title);

        $tableData = [];

        // Show records to process
        $recordsToProcess = $stats['records_to_process'];
        $status = $recordsToProcess > 0 ? 'Ready for migration' : 'No records to process';
        $tableData[] = ['Records to Process', number_format($recordsToProcess), $status];

        // Show field breakdown
        foreach (self::MIGRATION_FIELDS as $field) {
            $count = $stats[$field . '_count'];
            $tableData[] = ["- {$field}", number_format($count), 'Fields in records above'];
        }

        $tableData[] = ['Total Messages', number_format($stats['total_messages']), 'Information only'];

        $this->table(['Item', 'Count', 'Status'], $tableData);
        return $stats;
    }

    /**
     * Calculate migration statistics with optimized queries
     */
    private function calculateMigrationStatistics(): array
    {
        // Get total message count
        $totalMessages = DB::table('messages')->count();

        // Get field counts in single query
        $selects = [];
        foreach (self::MIGRATION_FIELDS as $field) {
            $selects[] = "SUM(CASE WHEN {$field} IS NOT NULL AND {$field} != '' THEN 1 ELSE 0 END) as {$field}_count";
        }

        $query = "SELECT " . implode(', ', $selects) . " FROM messages";
        $result = DB::selectOne($query);

        // Get records that have at least one field to process
        $whereConditions = [];
        foreach (self::MIGRATION_FIELDS as $field) {
            $whereConditions[] = "({$field} IS NOT NULL AND {$field} != '')";
        }
        $whereClause = implode(' OR ', $whereConditions);
        $recordsToProcess = DB::table('messages')->whereRaw($whereClause)->count();

        // Build stats array
        $stats = [
            'total_messages' => $totalMessages,
            'records_to_process' => $recordsToProcess
        ];

        foreach (self::MIGRATION_FIELDS as $field) {
            $stats[$field . '_count'] = $result->{$field . '_count'} ?? 0;
        }

        return $stats;
    }

    /**
     * Confirm migration execution with user
     *
     * @param array $stats
     * @return bool
     */
    private function confirmMigrationExecution(array $stats): bool
    {
        $recordsToProcess = $stats['records_to_process'];

        if ($recordsToProcess === 0) {
            $this->warn('No records found to migrate');
            return false;
        }

        $this->warn("WARNING: This operation will modify {$recordsToProcess} message records");
        $this->warn('WARNING: Please ensure you have a recent database backup');

        return $this->confirm('Do you want to proceed with the migration?');
    }

    /**
     * Execute the migration process with transaction safety and realtime verification
     *
     * @param int $batchSize
     * @param array $stats
     * @return bool
     */
    private function executeMigrationProcess(int $batchSize, array $stats): bool
    {
        $this->info('Starting encryption migration process with realtime verification...');

        $startTime = microtime(true);

        try {
            $migrationStats = $this->processMigration($batchSize, $this->option('dry-run'));

            $totalProcessed = $migrationStats['records_processed'];
            $fieldUpdates = $migrationStats['field_updates'];
            $totalErrors = $migrationStats['errors'];
            $chunksProcessed = $migrationStats['chunks_processed'] ?? 0;
            $chunksSuccessful = $migrationStats['chunks_successful'] ?? 0;

            $executionTime = microtime(true) - $startTime;

            $this->displayMigrationResults($totalProcessed, $fieldUpdates, $totalErrors, $executionTime, $stats);

            if ($this->option('dry-run')) {
                $this->info("Dry-run summary: {$chunksSuccessful}/{$chunksProcessed} chunks successful");
                return $totalErrors === 0;
            } else {
                $this->info("Migration summary: {$chunksSuccessful}/{$chunksProcessed} chunks successful");
                return $totalErrors === 0;
            }
        } catch (Exception $e) {
            $this->error('Migration failed: ' . $e->getMessage());
            Log::error('Migration execution error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Process migration for all records with multiple fields
     *
     * @param int $batchSize
     * @param bool $isDryRun
     * @return array
     */
    private function processMigration(int $batchSize, bool $isDryRun = false): array
    {
        // Build query and get total count
        $whereClause = $this->buildWhereClause();
        $totalCount = $this->getTotalRecordCount($whereClause);

        if ($totalCount === 0) {
            $this->line("No records found with any fields to process");
            return ['records_processed' => 0, 'field_updates' => 0, 'errors' => 0];
        }

        $this->info("Processing {$totalCount} records with realtime verification...");

        // Initialize progress tracking
        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->setFormat('verbose');

        $stats = $this->initializeStats();
        $baseQuery = $this->buildMigrationQuery($whereClause);

        $totalProcessedRecords = 0;

        $this->processChunks(
            $baseQuery,
            $batchSize,
            $progressBar,
            $stats,
            $isDryRun,
            $totalCount
        );

        $progressBar->finish();
        $this->line('');

        $this->displayProcessingSummary($stats, $isDryRun);

        return [
            'records_processed' => $stats['recordsProcessed'],
            'field_updates' => $stats['fieldUpdates'],
            'errors' => $stats['errors'],
            'chunks_processed' => $stats['chunksProcessed'],
            'chunks_successful' => $stats['chunksSuccessful']
        ];
    }

    /**
     * Build WHERE clause for migration query
     *
     * @return string
     */
    private function buildWhereClause(): string
    {
        $whereConditions = [];
        foreach (self::MIGRATION_FIELDS as $field) {
            $whereConditions[] = "({$field} IS NOT NULL AND {$field} != '')";
        }
        return implode(' OR ', $whereConditions);
    }

    /**
     * Get total count of records to process
     *
     * @param string $whereClause
     * @return int
     */
    private function getTotalRecordCount(string $whereClause): int
    {
        return DB::table('messages')->whereRaw($whereClause)->count();
    }

    /**
     * Initialize statistics tracking
     *
     * @return array
     */
    private function initializeStats(): array
    {
        return [
            'recordsProcessed' => 0,
            'fieldUpdates' => 0,
            'errors' => 0,
            'chunksProcessed' => 0,
            'chunksSuccessful' => 0,
            'totalProcessedRecords' => 0
        ];
    }

    /**
     * Build migration query with all required fields
     *
     * @param string $whereClause
     * @return \Illuminate\Database\Query\Builder
     */
    private function buildMigrationQuery(string $whereClause)
    {
        $selectFields = ['id'];

        // Add decrypted fields
        foreach (self::MIGRATION_FIELDS as $field) {
            $selectFields[] = DB::raw("AES_DECRYPT(FROM_BASE64({$field}), '{$this->oldKey}') as {$field}_decrypted");
        }

        // Add original encrypted fields
        $selectFields = array_merge($selectFields, self::MIGRATION_FIELDS);

        return DB::table('messages')
            ->whereRaw($whereClause)
            ->select($selectFields)
            ->orderBy('id');
    }

    /**
     * Process all chunks with optimized performance
     *
     * @param \Illuminate\Database\Query\Builder $baseQuery
     * @param int $batchSize
     * @param object $progressBar
     * @param array $stats
     * @param bool $isDryRun
     * @param int $totalCount
     */
    private function processChunks($baseQuery, int $batchSize, $progressBar, array &$stats, bool $isDryRun, int $totalCount): void
    {
        $baseQuery->chunkById($batchSize, function ($messages) use (
            $progressBar,
            &$stats,
            $isDryRun,
            $totalCount
        ) {
            $stats['chunksProcessed']++;

            DB::beginTransaction();

            try {
                $chunkStats = $this->processMessagesInChunk($messages, $progressBar, $stats, $totalCount);
                $this->handleChunkResult($chunkStats, $stats, $isDryRun);
            } catch (Exception $e) {
                DB::rollBack();
                $stats['errors']++;
                $this->error("Chunk {$stats['chunksProcessed']} failed: " . $e->getMessage());
            }

            return $stats['totalProcessedRecords'] < $totalCount;
        }, 'id');
    }

    /**
     * Process all messages in a single chunk
     *
     * @param object[] $messages
     * @param object $progressBar
     * @param array $stats
     * @param int $totalCount
     * @return array
     */
    private function processMessagesInChunk($messages, $progressBar, array &$stats, int $totalCount): array
    {
        $chunkStats = ['records' => 0, 'field_updates' => 0, 'errors' => 0];

        foreach ($messages as $message) {
            try {
                $result = $this->processMessageFields($message);
                if ($result['success']) {
                    $chunkStats['field_updates'] += $result['fields_updated'];
                    $chunkStats['records']++;
                } else {
                    $chunkStats['errors']++;
                }
            } catch (Exception $e) {
                $this->logMessageError($message->id, $e);
                $chunkStats['errors']++;

                // Enhanced error display for dry-run mode
                if ($this->option('dry-run')) {
                    $this->error("DRY-RUN ERROR: Message ID {$message->id} - {$e->getMessage()}");
                }
            }

            $stats['totalProcessedRecords']++;
            $progressBar->advance();

            if ($stats['totalProcessedRecords'] >= $totalCount) {
                break;
            }
        }

        return $chunkStats;
    }

    /**
     * Handle chunk result and transaction decision
     *
     * @param array $chunkStats
     * @param array $stats
     * @param bool $isDryRun
     */
    private function handleChunkResult(array $chunkStats, array &$stats, bool $isDryRun): void
    {
        $chunkNumber = $stats['chunksProcessed'];

        if ($chunkStats['errors'] > 0) {
            DB::rollBack();
            $stats['errors'] += $chunkStats['errors'];

            // Enhanced error reporting for dry-run mode
            if ($isDryRun) {
                $this->error("DRY-RUN: Chunk {$chunkNumber} encountered {$chunkStats['errors']} errors - would fail in real migration");
                $this->warn("These errors need to be fixed before running actual migration");
            } else {
                $this->error("Chunk {$chunkNumber} failed with {$chunkStats['errors']} errors - rolled back");
            }
            return;
        }

        // Success case
        $stats['chunksSuccessful']++;
        $stats['recordsProcessed'] += $chunkStats['records'];
        $stats['fieldUpdates'] += $chunkStats['field_updates'];

        if ($isDryRun) {
            DB::rollBack();
            $this->logChunkProgress($chunkNumber, 'Dry-run', 'processed successfully - rolled back');
        } else {
            DB::commit();
            $this->logChunkProgress($chunkNumber, 'Chunk', 'committed successfully');
        }
    }

    /**
     * Log chunk progress with reduced frequency
     *
     * @param int $chunkNumber
     * @param string $prefix
     * @param string $message
     */
    private function logChunkProgress(int $chunkNumber, string $prefix, string $message): void
    {
        if ($chunkNumber % self::CHUNK_LOG_FREQUENCY === 0 || $chunkNumber === 1) {
            $this->info("{$prefix}: Chunk {$chunkNumber} {$message}");
        }
    }

    /**
     * Display processing summary
     *
     * @param array $stats
     * @param bool $isDryRun
     */
    private function displayProcessingSummary(array $stats, bool $isDryRun): void
    {
        $mode = $isDryRun ? 'Dry-run completed' : 'Migration completed';
        $this->info("{$mode} - Chunks: {$stats['chunksSuccessful']}/{$stats['chunksProcessed']} successful, Records: {$stats['recordsProcessed']}, Field updates: {$stats['fieldUpdates']}, Errors: {$stats['errors']}");
    }



    /**
     * Log message processing error
     *
     * @param int $messageId
     * @param Exception $e
     */
    private function logMessageError(int $messageId, Exception $e): void
    {
        $this->warn("Error processing message ID {$messageId}: " . $e->getMessage());
        Log::error("Migration error ID {$messageId}: " . $e->getMessage(), [
            'message_id' => $messageId,
            'error' => $e->getMessage()
        ]);
    }

    /**
     * Process all fields for a single message record
     *
     * @param object $message
     * @return array
     * @throws Exception
     */
    private function processMessageFields(object $message): array
    {
        $fieldsUpdated = 0;
        $updateData = [];

        // Simulate errors for testing in dry-run mode
        if ($this->option('dry-run') && $this->shouldSimulateError($message->id)) {
            throw new Exception("SIMULATED ERROR: Testing error handling for message ID {$message->id}");
        }

        // Process each field for this message
        foreach (self::MIGRATION_FIELDS as $field) {
            $decryptedField = $field . '_decrypted';
            $originalValue = $message->{$field};
            $decryptedValue = $message->{$decryptedField};

            // Skip if field is empty or null
            if (empty($originalValue)) {
                continue;
            }

            // Check if decryption failed (would be null)
            if ($decryptedValue === null) {
                $this->error("Decryption failed for message ID: {$message->id}, field: {$field}");
                Log::error("Decryption failure during migration", [
                    'message_id' => $message->id,
                    'field' => $field,
                    'original_data_preview' => substr($originalValue, 0, self::LOG_PREVIEW_LENGTH) . '...'
                ]);
                throw new Exception("Decryption failed for message ID: {$message->id}, field: {$field}");
            }

            try {
                // Encrypt with new key
                $newEncryptedContent = $this->encryptWithNewKey($decryptedValue, $field);

                // Realtime verification
                $verifyResult = $this->verifyEncryptionRealtime($newEncryptedContent, $decryptedValue, $message->id, $field);
                if (!$verifyResult) {
                    throw new Exception("Decryption failed for message ID: {$message->id}, field: {$field}");
                }

                // Add to update batch
                $updateData[$field] = $newEncryptedContent;
                $fieldsUpdated++;
            } catch (Exception $e) {
                throw new Exception("Error processing field {$field}: " . $e->getMessage());
            }
        }

        // Single update query for all fields of this message
        if (!empty($updateData)) {
            DB::table('messages')
                ->where('id', $message->id)
                ->update($updateData);
        }

        return [
            'success' => true,
            'fields_updated' => $fieldsUpdated
        ];
    }

    /**
     * Verify encryption by decrypting with new key and comparing with original plain text
     *
     * @param string $newEncryptedData
     * @param string $originalPlainText
     * @param int $messageId
     * @param string $field
     * @return bool
     */
    private function verifyEncryptionRealtime(string $newEncryptedData, string $originalPlainText, int $messageId, string $field): bool
    {
        try {
            // Decrypt the newly encrypted data using new key
            $verifyQuery = DB::selectOne("SELECT AES_DECRYPT(FROM_BASE64(?), ?) as decrypted_result", [
                $newEncryptedData,
                $this->newKey
            ]);

            $decryptedResult = $verifyQuery->decrypted_result ?? null;

            // Compare with original plain text
            if ($decryptedResult === $originalPlainText) {
                return true; // Verification successful
            } else {
                // Log detailed error for debugging
                $originalPreview = strlen($originalPlainText) > self::LOG_PREVIEW_LENGTH ? substr($originalPlainText, 0, self::LOG_PREVIEW_LENGTH) . '...' : $originalPlainText;
                $decryptedPreview = $decryptedResult ? (strlen($decryptedResult) > self::LOG_PREVIEW_LENGTH ? substr($decryptedResult, 0, self::LOG_PREVIEW_LENGTH) . '...' : $decryptedResult) : 'NULL';

                $this->error("Verification mismatch for message ID {$messageId}, field {$field}:");
                $this->error("  Original: {$originalPreview}");
                $this->error("  Decrypted: {$decryptedPreview}");
                Log::error("Verification mismatch ID {$messageId} field {$field}", [
                    'message_id' => $messageId,
                    'field' => $field,
                    'original_preview' => $originalPreview,
                    'decrypted_preview' => $decryptedPreview
                ]);

                return false;
            }
        } catch (Exception $e) {
            $this->error("Verification error for message ID {$messageId}, field {$field}: " . $e->getMessage());
            Log::error("Verification error ID {$messageId} field {$field}: " . $e->getMessage(), [
                'message_id' => $messageId,
                'field' => $field,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Encrypt content with new key, field-specific handling
     */
    private function encryptWithNewKey(?string $content, string $field): ?string
    {
        // Handle field-specific null/empty logic
        if ($field === 'content_text' && $content === null) {
            return '';
        }

        if ($field === 'quote_text' && empty($content)) {
            return null;
        }

        if (!in_array($field, self::MIGRATION_FIELDS)) {
            throw new Exception("Unsupported field: {$field}");
        }

        // Encrypt the content
        $processedKey = $this->generatePassphrase($this->newKey);
        $encrypted = openssl_encrypt($content, 'AES-128-ECB', $processedKey, OPENSSL_RAW_DATA);

        if ($encrypted === false) {
            throw new Exception("Encryption failed for {$field}");
        }

        return base64_encode($encrypted);
    }

    /**
     * Process key like MySQL does - XOR folding to 16 bytes
     * This method creates a fixed-length key by:
     * - Creating an empty byte array of specified length
     * - Iterating through each character in the original key
     * - Applying XOR between ASCII value of character and array element
     * - Ensures compatibility with MySQL's AES_ENCRYPT/AES_DECRYPT key processing
     *
     * @param string $key
     * @param int $length
     * @return string
     */
    private function generatePassphrase(string $key, int $length = 16): string
    {
        $result = array_fill(0, $length, 0);
        foreach (str_split($key) as $i => $char) {
            $result[$i % $length] ^= ord($char);
        }
        return implode(array_map("chr", $result));
    }

    /**
     * Simulate errors for testing purposes in dry-run mode
     * Creates errors for every 5th message to test error handling
     *
     * @param int $messageId
     * @return bool
     */
    private function shouldSimulateError(int $messageId): bool
    {
        // Create error for every 5th message (IDs ending in 0 or 5)
        return ($messageId % 5 === 0);
    }

    /**
     * Display final migration results
     *
     * @param int $recordsProcessed
     * @param int $fieldUpdates
     * @param int $totalErrors
     * @param float $executionTime
     * @param array $stats
     * @return void
     */
    private function displayMigrationResults(int $recordsProcessed, int $fieldUpdates, int $totalErrors, float $executionTime, array $stats): void
    {
        $isDryRun = $this->option('dry-run');

        $this->line('');
        if ($isDryRun) {
            $this->info('=== DRY-RUN COMPLETED ===');
            $this->warn('This was a DRY-RUN - no actual data was modified');
        } else {
            $this->info('=== MIGRATION COMPLETED ===');
        }

        $this->info("Records processed: " . number_format($recordsProcessed));
        $this->info("Field updates " . ($isDryRun ? "simulated" : "completed") . ": " . number_format($fieldUpdates));
        $this->info("Execution time: " . round($executionTime, 2) . " seconds");

        if ($totalErrors > 0) {
            $this->error("Total errors encountered: {$totalErrors}");
            if ($isDryRun) {
                $this->error('DRY-RUN FAILED - These errors must be fixed before running actual migration');
                $this->warn('Review the error messages above and fix the issues before proceeding');
            } else {
                $this->warn('Migration completed with errors - please review the error messages above');
            }
        } else {
            if ($isDryRun) {
                $this->info('DRY-RUN completed successfully - no errors detected');
                $this->info('You can now run the actual migration with confidence');
            } else {
                $this->info('Migration completed successfully with realtime verification');
                $this->info('All processed records have been verified during the migration process');
            }
        }
    }
}
